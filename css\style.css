@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root {
    --primary-color:#ff7800;
    --secondary-color: #7af75b;
    --secondary-dark-color: #79800559;
    --black: #130f40;

    --main_color : #ff8716;
    --p_color : #7b7b7b;
    --bg_color : #f3f3f3;
    --white_color : #fff;
    --color_heading : #121416;
    --border_color : #e5e5e5d5;
    --Sale_color : #E51A1A;

    
    --light-bg-color:#f2f3f5;
    --light-text-color:#7c899a;
    --border-color: #e5e8ec;
    --dark-color:#0a021c;

    --font-small: 13px;
    --font-smaller: 11px;

    --percent100: 100%;
    --percent50: 50%;

    --fw3: 300;
    --fw5: 500;
    --fw6: 600;
    --fw7: 700;
    --fw8: 800;

    --trans-background-color: background-color .3s, color .3s;
    --trans-background: background-color .3s;
    --trans-color: color .3s;


}

*{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    list-style: none;
    text-decoration: none;
    font-family: "Inter" , sans-serif;
    color: var(--color_heading);
}
*,::before,::after {
    box-sizing: border-box;
}

body{
    /* padding-top: 140px; */
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    font-weight: 400;
    /* line-height: 1.4; */
    color: var(--dark-color);
    background-color: var(--white_color);

    
}


a {
    text-decoration: none;
    color: inherit;
    -webkit-tap-highlight-color: transparent;
}

ul {
    list-style: none;
}
img {
    max-width: var(--percent100);
    vertical-align: middle;
}
strong {
    font-weight: var(--fw8);
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
input::placeholder {
    font: inherit;
}


h1,h2,h3,h4,h5,h6{
    color: var(--color_heading);
    font-family: "DM Sans" , sans-serif;
    font-family: 'Poppins';

}

h1 {
    font-size: calc(0.9em + 1vw);
    font-weight: var(--fw6);
    /* line-height: 1; */
}
h2 {
    font-size: 2.5em;

}
h3 {
    font-size: 1.2em;
    font-weight: var(--fw7);
    /* margin-top: 15px; */

}
h4 {
    font-size: 1em;
    font-weight: var(--fw6);
}

span{
    color: var(--main_color);
}
p{
    color: var(--p_color);
}
/* 
img{
    width: 100%;
} */
input,select,button{
    border: none;
    outline: none;
    background: white;
}
.btns{
    display: flex;
    align-items: center;
    gap: 20px;
}
.btn{
    padding: 10px 18px;
    text-transform: capitalize;
    border-radius: 5px;
    cursor: pointer;
    background: var(--main_color);
    color: var(--white_color);
    display: flex;
    gap: 10px;
    align-items: center;
    transition: 0.3s;
}
.btn i{
    color: var(--white_color);
}
.btn:hover{
    scale: 1.1;
}


.fa-angle-right{
    position: absolute;
    --fa: "\f105";
    right: 15px;
}

/*-----------------------
 * REUSABLE SELECTION  --
  *----------------------*/

  
.flexwrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}
.flexcenter {
    display: flex;
    align-items: center;
    justify-content: center;
}
.flexitem {
    display: flex;
    align-items: center;
}
.flexcol {
    display: flex;
    flex-direction: column;
    gap: 1em;
}


.circle {
    position: absolute;
    top: -15px;
    left: 0;
    width: 38px;
    height: 38px;
    border-radius: var(--percent50);
    background-color: var(--light-bg-color);
}
.circle::before {
    content: '';
    position: absolute;
    width: 28px;
    height: 28px;
    border-radius: var(--percent50);
    background-color: var(--secondary-color);
    bottom: 5px;
    right: 5px;
    opacity: .4;
    
}

.mobile-hide {
    display: none;
}

.main-links a:hover {
color: var(--secondary-color);
}

.seconde-links a:hover {
color: var(--light-text-color);
}
.icon-small, .icon-large {
display: flex;
align-items: center;
padding: 0 0.25em;
font-weight: normal;
}
.icon-small {
font-size: 1.25em;
margin-left: auto;
}
.icon-large {
font-size: 1.75em;
padding: 0 0.75em 0 0;
}


.object-cover img {
    position: absolute;
    object-fit: cover;
    width: var(--percent100);
    height: var(--percent100);
    /* display: block; */
}
.products .media {
    position: relative;
    flex-shrink: 0;
    overflow: hidden;
}

.primary-button, .secondary-button, .light-button {
    font-size: var(--font-small);
    padding: 0.5em 1.35em;
    height: auto;
    width: fit-content;
    border-radius: 2em;
    transition: var(--trans-background-color);
}
.primary-button {
    background-color: var(--primary-color);
    color: var(--white_color);
}
.primary-button:hover {
    background-color: var(--dark-color);
}
.secondary-button {
    background-color: var(--secondary-dark-color);
    color: var(--white_color);
}
.secondary-button:hover {
    background-color: var(--light-bg-color);
    color: var(--secondary-dark-color);
}
.light-button { 
    background-color: var(--light-bg-color);
    
}
.light-button:hover{
    background-color: var(--secondary-dark-color);
    color: var(--white_color);
}
.view-all {
    font-size: var(--font-small);
    display: flex;
    gap: 1em;
    transition: var(--trans-color);
}
.mini-text {
    font-size: var(--font-smaller);
    color: var(--light-text-color);
}

.collapse {
    display: block;  /* add by me  */
    margin-top: 2em;
}
.productsDetils{
    display: none;
}
/* start login page  */

/* .login-page {
    height: 40vh;
} */

.login-page form {
    max-width: 300px;
    margin: auto;
}
.login-page form input {
    margin-bottom: 10px;
}
.text-center {
    text-align: center;
}



.login-page h1,
.login-page h1 span {
    cursor: pointer;
    color: paleturquoise;
}

.login-page span.selected {
    color: var(--primary-color);
} 

.login-page .signup {
    display: none;
}



/* end login page  */


.page-home{
    margin-top: 135px;
}

/* start profile page  */
.panel-body{
    margin-bottom: 20px;
}
.panel-body a.infoedit {
    color: var(--secondary-color);
    font-size: var(--font-small);
    margin-left: 60px;

}


/* end profile page  */


 /* single page - breadcrumb  */
.breadcrumb{
    position: relative;
    font-size: var(--font-small);
    padding: 5px 20px;
    padding-bottom: 0;
    margin-bottom: 0;
    background: var(--bg_color);
  
    width: 100%;
  
    background: var(--bg_color);

    /* z-index: 1; */
}

.breadcrumb .flexitem{
    padding: 0;
    margin: 0;
}
.breadcrumb li:not(:last-child)::after {
    content: '/';
    padding: 0 0.35em;
}
.breadcrumb li:last-child {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--light-text-color);
}


/* product details - image view */

.product{
    /* margin: 5px; */
    width: 220px;
    /* height: 300px; */
}

.single-product .wrapper {
    width: 100%;
    margin-top: 7.5em;
    
}


.products.one .flexwrap {
    flex-wrap: nowrap;
}
.products.one .item {
    position: relative;
    flex-direction: column;
    /* margin-top: 15px; */
}


.products.one .item h1 {
margin-bottom: 25px;
}



.products.one .price-tag .discount {
    font-size: 1em;
    font-weight: var(--fw7);
    line-height: 1;
    position: absolute;
    top: 3%;
    bottom: auto;
    right: 10%;
    padding: 1em;
    z-index: 2;
    background-color: var(--dark-color);
    color: var(--white_color);
    border-radius: var(--percent50);
    margin: 0.5em;
}
.products.one :where(.big-image, .small-image) {
    overflow: hidden;

}

.products.one .big-image{
    position: relative;
    /* margin-bottom: 1em; */
    width: 100%;
    height: auto;
    justify-content: center;
    align-items: center;
}

.products.one .big-image img{
    object-fit: contain;
    padding: 30px 10px;
    width: 100%;
    height: 320px;   /* adjust the big image height */
    display: block;
}
.products.one .small-image{
    padding-left: 25px;
    width: 100%;
    height: auto;
}
.products.one .small-image img{
    height: 100%;
    height: 60px;    /* adjust the big image height */
}

.products.one .small-image .swiper-slide-thumb-active img{
    border: 2px solid var(--primary-color);
    border-radius: 5px;
}
/* .products.one :where(.big-image, .small-image) img {
    object-fit: cover;
    width: var(--percent100);
    height: var(--percent100);
    display: block;
} */
.products.one .thumbnail-show{
    position: relative;
    display: flex;
    /* width: 80%; */
    height: auto;
    overflow: hidden;
    border-radius: 5px;
    margin: 0 -20px;
    /* border: 1px solid black; */
    align-items: center;
    justify-content: space-around;

}
.products.one .thumbnail-show .swiper-slide-next {
    transform: scale(1.05);
    border: 1px solid var(--primary-color);
}

.products :is(.swiper-button-next, .swiper-button-prev) {
    outline: 0;
    /* color: var(--secondary-dark-color); */
    transition: var(--trans-background), transform .3s;
}
.products .big-image:hover .swiper-button-next {
transform: translateX(5px);
}

.products .big-image:hover .swiper-button-prev {
transform: translateX(-5px);
    
}

.products :is(.swiper-button-next, .swiper-button-prev)::after {
    font-size: 1.5em;
}
.products .big-image:hover :is(.swiper-button-next, .swiper-button-prev) {
    background-color: var(--light-bg-color);
}



/* product details - Product details */



.products .rating {
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin-top: 0.1em;
}
.products .rating .stars {
    width: 80px;
    height: 15px;
    margin-top: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16' fill='rgba(240,187,64,1)'%3E%3Cpath d='M12.0006 18.26L4.94715 22.2082L6.52248 14.2799L0.587891 8.7918L8.61493 7.84006L12.0006 0.5L15.3862 7.84006L23.4132 8.7918L17.4787 14.2799L19.054 22.2082L12.0006 18.26Z'%3E%3C/path%3E%3C/svg%3E");
    background-position-y: center;
    background-repeat-y: no-repeat;
    
    
}

.products .rating .stars i{
    text-decoration: none;
}
/* to set width of stars  */
.products .item:where(:nth-child(3),:nth-child(3)) .stars {
    width: 64px;
}
.products .stock-sku{
    padding: 8px 0px;
}
.products.one .available {
    font-size: var(--font-small);
    font-weight: var(--fw5);
    padding: 0.5em;
    margin-right: 1em;
    border-radius: 3px;
    color: #10ac84;
    background-color: var(--light-bg-color);
}
.products.one .add-review{
    color: var(--dark-color);
}
.products.one .price {
    display: flex;
    align-items: flex-start;
    margin: 5px 0;

}
.products .variant form{
    display: flex;
    margin-top: 0.5em;
}
.products .variant form p{
    position: relative;
    margin: 0 0.5em 0.5em 0;
}
.products :where(.variant, .actions) .circle {
    display: block;
    position: static;
    top: 0;
    margin: 0;
    cursor: pointer;
    z-index: 1;
}
.products .variant input{
    clip: rect(0,0,0,0);
    overflow: hidden;
    position: absolute;
    height: 0;
    width: 0;
 }
 .products .variant label::before {
    opacity: 1;
 }
 .products .variant label[for="cogrey"]::before {
    background-color: #576574;
 }

 .products .variant label[for="coblue"]::before {
    background-color: #45a0ff;
 }
 .products .variant label[for="cogreen"]::before {
    background-color: #1dd1a1;
 }
 .single-product .variant form p input:checked + label {
    background-color: transparent;
    border: 2px solid var(--dark-color);
    color: var(--white-color);
 }
 .products .sizes .variant label::before {
    background-color: var(--white-color);
 }
 .products .sizes .variant label span {
    position: absolute;
    top: 0;
    left: 0;
    width: var(--percent100);
    height: var(--percent100);
    font-size: 0.85em;
    display: flex;
    align-items: center;
    justify-content: center;
 }

 .single-product .sizes .variant form p input:checked + label::before {
    background-color: var(--dark-color);
    opacity: 1;
 }

 .products .actions {
    display: flex;
    position: absolute;
    top: 55px;
    right: 40px;
    /* width: 100%; */
    flex-direction: column;
    flex-wrap: wrap;
    margin: 5px;
    padding: 5px;
    gap: 9px;
    justify-content: center;
    align-items: flex-end;
 }
 .products .qty-control {
    width: fit-content;
    padding: 0.5em;
    border: 1px solid var(--border-color);
    margin: 10px auto;
    border-radius: 25px
 }
 .products .actions :where(input, button) {
    font-size: 1.2em;
    outline: 0;
    border: 0;
 }
 .products .actions input {
    width: 50px;
    text-align: center;
 }
 .products .qty-control button::before {
    background-color: transparent;
 }
 .products .actions .button-cart {
    flex: 1;
    margin: 5px auto;
 }
 .products .actions .button-cart button {
    /* width: var(--percent100); */
    cursor: pointer;

 }
    .products .actions .button-cart button:hover {
        background-color: var(--secondary-color);
        color: var(--white_color);
    }
 .products .actions .button-cart .btn_add_cart.active {
    background-color: var(--white_color);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    pointer-events: none;
 }

 .wish-share a {
    display: flex;
    align-items: center;
    text-transform: uppercase;
    font-weight: var(--fw7);
    margin: 1em 2em 1em 0;
    transition: var(--trans-color);
 }


 /* single page - product review  */
 
 .products .collapse .has-child > a {
    position: relative;
    font-weight: var(--fw6);
    font-size: 15px;
    text-transform: capitalize;
    padding: .6em 1.25em;
    border-top: 1px solid var(--border-color);
    display: block;
    cursor: pointer;
 }
 .products .collapse .has-child > a::before {
    content: '+';
    position: absolute;
    left: 0;
 }
 .products .collapse .has-child.expand > a::before {
    content: '-';
 }
 .products .collapse .content {
    margin: 0 0 1.5em 2em;
    font-size: var(--font-small);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
 }
 .products .collapse .has-child.expand > .content {
    max-height: 1000px; /* Large enough to accommodate content */
    transition: max-height 0.5s ease-in;
 }
 .products .collapse .content li span:first-child {
    min-width: 100px;
    display: inline-flex;
    font-weight: var(--fw7);
    text-transform: uppercase;
    padding-top: 8px;
    color: var(--p_color);

}
.products .collapse .content li span:last-child {
    color: var(--p_color);


}
.products .collapse table {
    line-height: 3em;
}
.products .collapse table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--dark-color);
}





.products .collapse table :where(th, td) {
    border-bottom: 1px solid var(--border-color);
    padding-left: 2em;
}
.products .collapse .content {
    display: none;
}
.products .collapse .expand .content {
    display: flex;
    flex-direction: column;
}
.products .collapse .expand > a::before {
    content: '-';
}

/* 8.e page single - product review form  */
.products .reviews h4 {
    font-size: 2em;
    color: var(--light-text-color);
    padding-top: 1em;
    margin: 1em 0 0.5em;
    border-top: 1px solid var(--border-color);
}
.products .review-block {
    color: initial;
}
.products .review-block-head > a {
    display: block;
    font-size: 1.25em;
    width: var(--percent100);
    margin-top: 1em;
    text-align: center;
}
.products .review-block-head .rate-sum {
    position: relative;
    font-size: 4em;
    font-weight: var(--fw7);
    padding-right: 0.5em;
}
.products .review-block-head .rate-sum::before {
    content: '';
    width: 2px;
    height: 50px;
    display: block;
    position: absolute;
    top: 10px;
    right: 13px;
    transform: rotate(22deg);
    background-color: var(--light-bg-color);
}



.products .review-block-body .item{
    grid: 1em;
    padding-top: 2.5em;
    margin-top: 3.5em;
}
.products .review-block-body .second-links{
    margin-top: 1.5em;
}



.products .review-block-body :where(.person, .review-title) {
    font-weight: var(--fw7);
    text-transform: uppercase;
}
.products .review-block-body :where(.review-title, .view-all) {
    font-size: 1.25em;
    justify-content: end;
}
.products .review-block-body .item:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}

/* 8.e page single - product review form  */

.review-form .rating {
    flex-wrap: wrap;

}
.review-form p {
    font-size: 1.25em;
    margin-right: 1em;
}
.review-form .rate-this input {
    display: none;
}
.review-form .rate-this label {
    float: right;
    font-size: 2em;
    color: var(--secondary-dark-color);
}
.rate-this > input:checked ~ label,
.rate-this:not(:checked) > label:hover,
.rate-this:not(:checked) > label:hover ~ label {
    color: #f1c40e;
}
.rate-this > input:checked + label:hover,
.rate-this > input:checked ~ label:hover,
.rate-this > label:hover ~ input:checked ~ label,
.rate-this > input:checked ~ label:hover ~ label {
    color: #ffed85;
}
.review-form form {
    margin-top: 2.5em;
}
form p {
    display: flex;
    flex-direction: column;
    margin-bottom: 2em;
}
form :where(input, textarea) {
    line-height: 1;
    padding: 1em;
    border: 1px solid var(--border-color);
    outline: 0;
}
form label {
    font-weight: var(--fw5);
    margin-bottom: 0.5em;
    text-transform: uppercase;
}







/* 9 page single - special offer  */
.page-single .stock {
    margin: 2em 0;
 }
 .page-single .stock .qty span:last-child {
    color: initial;
 }
 .single-product .stock .bar {
    height: 10px;
 }
 .page-single .stock .bar .available {
    margin: 0;
    border-radius: 0;
    background-color: var(--secondary-dark-color);
 }

.related-products .content {
    height: var(--percent100);
}
.related-products .offer {
    margin: 0;
}
.related-products .offer p {
    margin: 0 1em 0 0;
    text-transform: none;
    line-height: 1;
}
.related-products .offer ul li {
    width: 28px;
    height: 28px;
    font-size: var(--font-small);
    padding: 0.5em 0.25em;
}


.related-products .content .stock {
    margin-top: auto;
}

/* 10 page category  */

.single-category .holder {
    display: flex;
    flex-direction: column;
    position: relative;
    margin-top: 130px;
}
.single-category .sidebar,
.single-category .section {
       padding: 0 2rem

} 
/* 10.a page category - Sidebar products filter  */
 
.filter-block {
    padding: 0;
    margin: 0;
    
}
.filter-block:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}
.filter-block h4 {
    font-size: 1.25em;
    margin-bottom: 0.5em;
}
.filter-block li {
    display: flex;
    font-size: 0.9em;
    position: relative;
    line-height: 2;
}
.filter-block label .checked {
    width: 16px;
    height: 16px;
    position: relative;
    line-height: 0;
    display: inline-block;
    border: 2px solid var(--secondary-dark-color);
    vertical-align: text-top;
    margin: 0 7px 0 0;
    cursor: pointer;
}
.filter-block label .checked::before {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: var(--secondary-dark-color);
    top: 2px;
    left: 2px;
    opacity: 0;

}
.filter-block input:checked + label .checked::before {
    opacity: 1;
}
.filter-block li .count {
    margin-left: auto;
    color: var(--light-text-color);
}

:where(.products .variant, .filter-block:not(.pricing)) input {

    clip: rect(0,0,0,0);
    overflow: hidden;
    position: absolute;
    height: 0;
    width: 0;
}

.bycolor label[for="cored"]::before {
    background-color: var(--primary-color);
}
.bycolor label[for="colight"]::before {
    background-color: var(--border-color);
}
.bycolor input:checked + label {
    background-color: transparent;
    border: 2px solid var(--dark-color);
}
input[type="range"] {
    -webkit-appearance: none;
    width: var(--percent100);
}
input[type="range"]:focus {
    outline: 0;
}
input[type="range"]::-webkit-slider-runnable-track {
    width: var(--percent100);
    height: 5px;
    cursor: pointer;
    background-color: var(--border-color);
    box-shadow: none;
    border: 0;
}
input[type="range"]::-webkit-slider-thumb {
    z-index: 2;
    position: relative;
    height: 18px;
    width: 14px;
    background-color: var(--primary-color);
    -webkit-appearance: none;
    margin-top: -7px;
}
.byprice .price-range {
    display: flex;
    justify-content: space-between;
    color: var(--light-text-color);
    margin-top: 0.5em;
}

/* 10.b page category - head & products block  */



.page-title {
    margin-bottom: 10px;
}
.cat-description p {
    font-weight: var(--fw3);
    font-size: 0.9em;
}
.cat-navigation {
    font-size: 0.85em;
    margin: 1em 0;
    justify-content: flex-end;
    gap: 1em;
}
.cat-navigation .item-filter {
    margin-right: auto;
}
.cat-navigation > div {
    position: relative;
}
.cat-navigation .item-filter a {
    align-items: center;
    text-transform: uppercase;
}
.cat-navigation ul {
    display: none;
    position: absolute;
    left: 0;
    min-width: var(--percent100);
    width: max-content;
    z-index: 10;
}
.cat-navigation .label, .cat-navigation ul li {
    display: flex;
    gap: 0.5em;
    padding: 0.5em 1em;
    transition: var(--trans-color);
}
:where(.item-sortir, .item-options) .label,
.cat-navigation ul {
    color: var(--light-text-color);
    background-color: var(--light-bg-color);
    cursor: pointer;
}
.cat-navigation :where(label, ul li):hover {
    color: var(--dark-color);
}
:where(.item-sortir, .item-options):hover ul {
    display: block;
}
.load-more {
    margin: 2em 0 4em;
}
.filter {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    padding: 2em;
    max-width: 400px;
    width: 90%;
    background-color: var(--white-color);
    overflow: auto;
    z-index: 1000;
    box-shadow: rgb(0 0 0 / 30%) 0 10px 50px;
    visibility: hidden;
    opacity: 0;
}
.filter.show {
    visibility: visible;
    background: white;
    opacity: 1;
} 



/* Modal  */


.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    max-width: 600px;
    width: 90%;
    height: max-content;
    padding: 2em;
    margin: auto;
    text-align: center;
    background-color: var(--light-bg-color);
    z-index: 1000;
    visibility: hidden;
    opacity: 0;
    transform: translateY(20px);
    transition: transform .2s, visibility .2s, opacity .2s;
}
.showmodal .modal {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
    transition: transform 1s, visibility 1s, opacity 1s;

}

.modal .image {
    position: relative;
    height: calc(150px + 10vw);
    overflow: hidden;
    max-height: 400px;
    margin: -2em -2em 0 -2em;
}

.modal h2 {
    font-size: 1.75em;
    text-transform: uppercase;
    max-width: 300px;
    margin: 0.5em auto;
    line-height: 1;
}

.modal p {
    margin-bottom: 1em;
    color: var(--light-text-color);
}

.modal .t-close {
    position: absolute;
    top: 1em;
    right: 1em;
}

.backtotop a {
    position: fixed;
    bottom: 6em;
    right: 2em;
    text-align: center;
    float: right;
    gap: 0;
    font-size: var(--font-small);
    padding: 0.5em 1em;
    border-radius: 3px;
    z-index: 999;
    background-color: var(--light-bg-color);
    transition: var(--trans-background-color);
}
.backtotop a:hover {
    background-color: var(--dark-color);
    color: var(--white-color);
    background: #10ac84;
}

































/* Products global CSS  */
.products.main {
    display: flex;
    flex-wrap: wrap;
    padding: 8px 15px;
}


.products.main .item {
    flex-direction: column;
    flex: 0 0 220px;
    padding: 0 0.938em;
    padding-bottom: 1em; /* it was 2em*/

}
.products .offer {
    text-align: center;
    margin-bottom: 1.5em;
}
.products .offer p {
    text-transform: uppercase;
    margin-bottom: 0.5em;
}
.products .offer ul {
    gap: 1em;
}
.products .offer ul li {
    position: relative;
    width: 34px;
    height: 34px;
    padding: 0.5em;
    line-height: initial;
    color: var(--secondary-dark-color);
    background-color: var(--light-bg-color);
    border-radius: 5px;
}
.products .offer ul li:not(:last-child)::before {
    content: ':';
    position: absolute;
    right: -0.6em;
    color: var(--light-text-color);
}
.products :where(.image, .thumbnail) img {
    transition: transform .3s;
}
.products :where(.image, .thumbnail):hover img {
    transform: scale(1.1);
}
.products .hoverable {
    position: absolute;
    top: 0;
    right: 0;
}
.products .hoverable li a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    padding: 0.25em;
    margin: 0.25em;
    border-radius: var(--percent50);
    line-height: 1;
    background-color: var(--white-color);
    opacity: .5;
    transition: var(--trans-background-color), opacity .3s;
}
.products .item:hover .hoverable li a {
    opacity: 1;
}
.products .hoverable li a:hover {
    color: var(--white-color);
    background-color: var(--dark-color);
}
.products .hoverable li.active a:hover {
    background-color: var(--primary-color);
}
.products .hoverable li:not(.active) {
    transform: translateX(100%);
    opacity: 0;
    transition: transform .3s, opacity .2s;
}
.products .item:hover .hoverable li {
    transform: translateX(0);
    opacity: 1;
}
.products .hoverable li:last-child {
    transition-delay: .1s;
}

.products .discount {
    top: auto;
    right: 0;
    left: auto;
    bottom: 0;
    background-color: transparent;
    z-index: 1;
}
.products .discount::before {
    background-color: var(--dark-color);
}
.products .discount span {
    position: relative;
    font-size: var(--font-smaller);
    font-weight: var(--fw5);
    color: var(--white-color);
}
.products .content {
    display: flex;
    flex-direction: column;
}
.products:where(.big, .main, .one) .content {
    gap: 0px; /* it was 1em*/
    margin-top: -20px;/* it was 1.25em*/
}
.products .rating {
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin-top: 0.75em;
}
.products .rating .stars {
    width: 80px;
    height: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16' fill='rgba(240,187,64,1)'%3E%3Cpath d='M12.0006 18.26L4.94715 22.2082L6.52248 14.2799L0.587891 8.7918L8.61493 7.84006L12.0006 0.5L15.3862 7.84006L23.4132 8.7918L17.4787 14.2799L19.054 22.2082L12.0006 18.26Z'%3E%3C/path%3E%3C/svg%3E");
    background-position-y: center;
    background-repeat-y: no-repeat;
    
    
}
/* to set width of stars  */
.products .item:where(:nth-child(3),:nth-child(3)) .stars {
    width: 64px;
}
.products h3 {
    font-family: 'Rubik';
    font-size: 1em;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    
}
.products h3 a:hover {
    text-decoration: underline;
}
.products .price .current {
    font-size: calc(0.8em + 1vw);
    color: var(--primary-color);
    margin-right: 0.25em; 
    display: flex;
    align-items: center;
}
.products .price .current h5{
    font-size: 0.46em;
    margin: auto 9px 10px 0;
}
.products .price .normal {
    display: flex;
    align-items: center;
    color: var(--light-text-color);
    text-decoration: line-through;
    position: relative;
    bottom: -4px;
}
.products .price .normal h5{
    font-size: 0.7em;
    margin: auto 3px 4.5px 0;
}
.products .stock .qty {
    display: flex;
    justify-content: space-between;
}
.products .stock .bar {
    height: 3px;
    margin-top: 1em;
    background-color: var(--border-color);
    overflow: hidden;
}
.products .stock .available {
    height: 3px;
    width: 21%;
    background-color: var(--secondary-color);
}
.products .item {
    display: flex;
    position: relative;
}
.products .stock-danger {
    color: var(--primary-color);
}
.flexwrap .row {
    flex: 0 0 100%;
    width: 100%;
    margin: 0;
    padding: 0.75em;
    border-radius: 5px;

}


/* 4.b big products  */
.products.big .item {
    flex-direction: column;
    padding: 1.5em;
    border: 2px solid var(--secondary-dark-color);
    border-radius: 7px;
    max-width: 460px;
    margin: 0 auto;
}



/* 4.c min product  */
.products.mini .item {
    margin: 1em;  /* it was margin-bottom 2em*/
}
.products.mini .media {
    width: 130px;
    height: 160px;
    margin-right: 1.25em;

}



.products.mini .content {
    margin: 0;
    gap: 0.5em; /* it was 0.75em*/
}
.products:where(.mini, .main) h3 {
    font-weight: 400;
}

/* Product description toggle styles */
.description.collapse {
    margin-top: 45px;
}

.description.collapse ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.description.collapse .has-child {
    margin-bottom: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
}

.description.collapse .has-child > a {
    position: relative;
    display: block;
    padding: 12px 15px;
    font-weight: 600;
    color: #333;
    background-color: #f9f9f9;
    cursor: pointer;
    border-radius: 5px;
}

.description.collapse .has-child > a::before {
    content: '+';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    transition: transform 0.3s ease;
}

.description.collapse .has-child.expand > a::before {
    content: '-';
}

.description.collapse .content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.description.collapse .has-child.expand > .content {
    padding: 15px;
    max-height: 500px; /* Adjust as needed */
    transition: max-height 0.5s ease-in, padding 0.3s ease-in;
}

.description.collapse .content p {
    margin: 0 0 10px 0;
}

.description.collapse .content ul {
    padding-left: 0;
}

.description.collapse .content ul li {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.description.collapse .content ul li:last-child {
    border-bottom: none;
}
































/* start header  */

header{
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    box-shadow: 5px 8px 8px #d1d1d13b;
    z-index: 1000;
    
}

header .top_header .container{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin: 10px 5px;
}

header .top_header .logo{
    /* width: 180px; */
    font-size: 20px;
    font-weight: bolder;
    display: flex;
    padding-left: 25px;
    line-height: 15px;
    justify-content: end;
    align-items: center;
    color: 2px solid rgba(0,0,0,.1) ;

}
header .top_header .logo i{
    color: var(--main_color);
    font-size: 40px;
    /* padding-left: 5%; */
}
header .top_header .logo span{
    color: #121416;
    font-size: 45px;
    padding-left: 15px;
}
header .top_header .search_box{
    width: 610px;
    display: flex;
    margin: 0 auto;
    align-items: center;
    border-radius: 2px;
}
header .top_header .search_box input{
    height: 40px;
    width: 400px;
    padding: 5px 15px 5px 10px;
    /* background: var(--bg_color); */
    
}
header .top_header .search_box .select_box {
    position: relative;
}
header .top_header .search_box .select_box::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 1px;
    height: 50%;
    background: #b9b9b9;

}
header .top_header .search_box select {
    height: 40px;
    width: 190px;
    background: var(--bg_color);
    font-size: 16px;
    cursor: pointer;
    padding-left: 10px;
    border-radius: 5px 0;
    margin-left: 10px;
}
header .top_header .search_box select option{
    font-size: 15px;
}
header .top_header .search_box button{
    height: 40px;
    width: 60px;
    border-radius: 0px 5px 5px 0px;
    background: var(--main_color);
    font-size: 18px;
    cursor: pointer;
}
header .top_header .search_box button i{
    color: var(--white_color);
}
header .top_header .header_icons{
    display: flex;
    gap: 30px;
    right: 20px;
}
header .top_header .header_icons {
    display: flex;
    position: absolute;
    top: 20px;
    right: 50px;
}
header .top_header .header_icons .icon{
    position: relative;
    cursor: pointer;

}
header .top_header .header_icons .icon i{
    font-size: 24px;
}
header .top_header .header_icons .icon .count{
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    background: var(--main_color);
    color: var(--white_color);
    font-size: 11px;
    border-radius: 100%;
}





/* stat of cat menu button  */

header .bottom_header .cat-menu {
    /* display: none; */
        width: -webkit-fill-available;
}

header .bottom_header .cat-menu  > ul > li {
    display: inline-block;
}

header .bottom_header .cat-menu  > ul > li:not(:last-child) {
  margin-right: 40px;
    
}
header .bottom_header {
    right: 0;
}
header .bottom_header .sub-menu{
 
    position: absolute;
    top: 100% !important;
    left: 0 !important;
    width: 230px;
    padding: 15px 0;
    background-color: var(--white_color);
    box-shadow: 0 0 5px hsla (0, 0%, 0%, 0.5);
    z-index: 1;
    transform-origin: top;
    transform: scaleY(0);
    visibility: hidden;
    opacity: 0;
}
header .bottom_header .sub-menu-right{
    left: 100% !important;
    top: 0 !important;

}
header .bottom_header li:hover > .sub-menu {
    opacity: 1;
    transform: none;
    visibility: visible;
    transition: all 0.5s ease;
}
header .bottom_header .sub-menu a {
    display: block;
    padding: 10px 24px;
    color: var(--black);
    font-size: 13px;
    text-decoration: none
}

header .bottom_header .sub-menu a {
    padding: 10px 24px;
}
header .bottom_header .sub-menu .dropdown > a {
    padding-right: 34px;
}

header .bottom_header ul li a span,
header .bottom_header .sub-menu span {
    background-image: linear-gradient(var(--primary-color), var(--primary-color));
    background-size: 0 2px;
    background-repeat: no-repeat;
    background-position: 0 100%;
    transition: background-size 0.5s ease;
    font-size: 16px;
    padding: 5px 0;
}
header .bottom_header ul li a {
    text-decoration: none;
}

header .bottom_header ul li a span:hover,
header .bottom_header .sub-menu li:hover > a > span {
    background-size: 100% 2px;
    text-decoration: none;
}


header .bottom_header .sub-menu i {
    position: relative;
    transform: rotate(-90deg);
    left: 10px;
}

header .bottom_header .menu-right{
    display: flex;
    position: absolute;
}
header .bottom_header .menu-right > * {
    margin-left: 25px;
}
header .bottom_header .menu-right .icon-btn{
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: hsl (0, 0%, 100%);
    font-size: 16px;
}
header .bottom_header .menu-right .open-menu-btn{
    display: none;
}












/* end of cat menu button  */



header .bottom_header{
    border-top: 1px solid var(--border_color);
    display: flex;

}
header .bottom_header .container{
    display: flex;
    width: 100%;
    position: relative;
    align-items: center;
    justify-content: flex-start;
}
header .bottom_header nav{
    display: flex;
    align-items: center;
    gap: 15px;
    height: 45px;
    margin: auto 20px;
}



/* new mega menu button  */
/* new mega menu button  */
/* new mega menu button  */


.menu-items {     /* old mega menu button  */
    display: none;
    /* gap: 15px; */
    align-items: center;
    /* justify-content: flex-start; */
    /* margin: auto 20px; */
}
.menu-items li{
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease-in-out;
}
.menu-items li:hover{
    background: var(--primary-color);

}
.menu-items li:hover a{
    text-decoration: none;

}

         /* dropdow  */

.dropdown {
    position: relative;
    display: flex;
}
.dropdown-menu{
    position: absolute;
    background: #122331;
    color: white;
    width: 100%;
    top: 50px;
    left: 0;
    opacity: 0;
    visibility: hidden;
    display: block;
    transition: all 0.3s ease-in-out;;
}
.menu-item {
    display: flex;
    justify-content: space-between;
    width: 100%;
}










/* new mega menu button  */
/* new mega menu button  */
/* new mega menu button  */






header .bottom_header .category_nav{
    width: 130px;
    position: relative;
    height: 70%;  /* Category btn hiegh*/
}

header .bottom_header .category_nav .category_btn{
    height: 100%;
    width: 100%;
    border-radius: 5px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* background: var(--main_color); */
    padding: 0 2px;
    cursor: pointer;
}

header .bottom_header .category_nav .category_btn i{
    color: var(--primary-color);
    margin-left: 2px;
}


header .bottom_header .category_nav .category_btn a{
    text-decoration: none;
    color: var(--primary-color);
}



header .bottom_header .category_nav .category_btn p{
    color: var(--primary-color);
    font-weight: 600;
    font-size: 15px;
    /* margin: auto; */
}
header .bottom_header .category_nav .category_btn p:hover{
    color: var(--secondary-color);

}

header .bottom_header .category_nav .category_nav_list{
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white_color);
    width: 100%;
    border: 1px solid #999;
    border-top: 0;
    display: flex;
    flex-direction: column;
    clip-path: polygon(0 0 , 100% 0 , 100% 0 , 0 0);
    transition: 0.3s ease-in-out;
    z-index: 5;

}
header .bottom_header .category_nav .category_nav_list.active{
    clip-path: polygon(0 0 , 100% 0 , 100% 100% , 0 100%);

}



header .bottom_header .category_nav .category_nav_list a{
    padding: 14px 10px;
    border-bottom: 1px solid var(--border_color);
    font-size: 14px;
    
}
header .bottom_header .category_nav .category_nav_list a:last-child{
    border-bottom: 0;
}
header .bottom_header .category_nav .category_nav_list a:hover{
    background: #d0d0d0;
}

header .bottom_header .nav_links{
    display: flex;
    gap: 35px;
    align-items: center;
    margin: 0;
    text-transform: capitalizeS;
}
header .bottom_header .nav_links li a{
    color: var(--color_heading);
    transition: 0.3s;
}

header .bottom_header .category_nav .category_btn i:hover,
header .bottom_header .category_nav .category_btn p:hover
{
    color: var(--secondary-color);
}
header .bottom_header .nav_links li:hover a,
header .bottom_header .nav_links li.active a{
    color: var(--main_color);
}
header .bottom_header .login_signup {
    display: flex;
    align-items: center;
    gap: 20px;
    position: absolute;
    right: 40px;
}
header .bottom_header .login_signup .btn{
    background:white;
    color: var(--main_color);
}
header .bottom_header .login_signup .btn i{
    color: var(--main_color);

}



/* slider  */

.slider{
    position: relative;
}
.slider .container{
    display: flex;
    justify-content: space-between;
    margin: 0;
}
.slider .banner_2{
    width: 23%;
    height: 100%;
    object-fit: cover;
}
.slider .banner_2 a{
    height: 100%;
    width: 100%;
}
.slider .container .slide-swp{
    width: 75%;
    overflow: hidden;
    position: relative;
    margin-left: 3rem
}
.swiper-wrapper{
    height: auto !important;
}
.slider .container .slide-swp .swiper-pagination span {
    background-color: #fff;
    opacity: 1;
}
/* .slider .container .slide-swp .swiper-button-next,
.slider .container .slide-swp .swiper-button-prev{
    color: var(--p_color);
} */
.swiper-pagination-bullet{
    background : var(--main_color) !important;
    width: 10px !important;
    height: 8px !important;
    border-radius: 20px !important;
}
.swiper-pagination-bullet-active{
    background: var(--main_color) !important;
    width: 20px !important;
    height: 8px !important;
    border-radius: 20px !important;
}


/* banners  */

.banners_4{
    margin: 40px 17px;
}
.banners_4 .container{
    display: flex;
    justify-content: space-between;
}
.banners_4 .container .box{
    width: 24%;
    background: url(../img/bg_banner3.jpg);
    background-size: cover;
    background-position: center;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 10px;
    position: relative;
}
.banners_4 .container .box img{
    width: 100px;
    transition: 0.3s;
}
.banners_4 .container .box:hover img{
    scale: 1.1;
}
.banners_4 .container .box h5{
    font-size: 16px;
}
.banners_4 .container .box .sale{
    display: flex;
    align-items: center;
    gap: 5px;
    margin: 7px 0;
}
.banners_4 .container .box .sale span{
    font-size: 25px;
    font-weight: bold;
}
.banners_4 .container .box h6{
    font-size: 14px;
    font-weight: bold;
}
.banners_4 .container .box .link_btn{
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
}

/* Start slide  */

.slide{
    margin:0 30px 70px 20px;
}
.top_slide{
    position: relative;
    margin-bottom: 20px;
    border-bottom: 3px solid var(--main_color);
}
.top_slide h2{
    position: relative;
    text-transform: uppercase;
    background: var(--main_color);
    color: var(--white_color);
    font-size: 18px;
    padding: 10px 40px;
    display: flex;
    align-items: center;
    gap: 10px;
    width: max-content;
}
.top_slide h2::before{
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    top: 0;
    z-index: 9;
    border-left: 20px solid transparent;
    background: #fff;
    left: 0;
    border-bottom: 20px solid #e26e02;
}
.top_slide h2 i{
    color: var(--white_color);
}
.slide .container{
    position: relative;
}
.slide .container .mySwiper{
    padding: 10px 0;
}
.slide .container .mySwiper .btn_Swip{
    position: absolute;
    top: 30px;
    right: 15px;
    background: var(--bg_color);
    color: var(--color_heading);
    font-weight: bold;
    border-radius: 5px;
    height: 35px;
    width: 35px;
    border: 1px solid var(--border_color);
}
.slide .container .mySwiper .btn_Swip::after{
    font-size: 12px;
}
.slide .container .mySwiper .btn_Swip.swiper-button-prev{
    left: calc(100% - 100px);
}

/* products card style  */



/* Product card styling */







































.mySwiper{
    overflow: hidden;
}
.product{
    background: #fff;
    padding: 20px;
    margin: 5px;
    box-shadow: 5px 5px 10px #94949428;
    border: 1px solid var(--border_color);
    border-radius: 5px;
    position: relative;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

}


.product:hover {
    transform: translateY(-5px);
    box-shadow: 5px 10px 20px rgba(0, 0, 0, 0.1);
}







.product .sale_percent{
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    background-color: #ed0505;
    color: var(--white_color);
    padding: 4px 10px;
    font-size: 13px;
}
.product .img_product{
    text-align: center;
    margin-bottom: 15px;
    /* flex: 0 0 auto; */
    /* height: 180px; */
    display: flex;



    justify-content: center;
    position: relative;
    height: 160px;
    width: 200px;
    padding: 0 20px;
    align-items: center;
    object-fit: contain;
    transition: 0.3s;
}




.product:hover .img_product{
    scale: 1.1;
}





.product .img_product img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}











.product .product_info {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}

.product .product_info h3 {
    font-size: 16px;
    margin: 0 0 10px 0;
    font-weight: 600;
    line-height: 1.4;
    height: 44px; /* Fixed height for title - approx 2 lines */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product .product_info .description {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    height: 40px; /* Fixed height for description - approx 2 lines */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}






.product .product_info .price {
    display: flex;
    align-items: center;
    margin-top: auto;
}

.product .product_info .price .current {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}








.product .product_info .price .old {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
    margin-left: 10px;
}

















.product .name_product{
    margin-bottom: 10px;
    height: 45px;
    color: var(--color_heading);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.product .name_product a{
    font-size: 16px;

}
.product .name_product a:hover{
    text-decoration: underline;
}
.stars{
    margin-top: 10px;
}
.stars i{
    color: var(--main_color);
    font-size: 14px;
    text-decoration: none;
}
.price{
    height: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 9px;
    
}
.price p{
    margin: 0;
    color: var(--main_color);
    font-weight: bold;
    font-size:16px;
}
.price .old_price{
    color: var(--p_color);
    text-decoration: line-through;
    font-size: 13px;
    font-weight: normal;
}
.product .icons{
    display: flex;
    gap: 10px;
    margin-top: 5px;
    height: 30px;
    align-items: center;
    justify-content: space-between;
}
.product .icons .icon_product{
    width: 30px;
    height: 30px;
    border: 1px solid var(--border_color);
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
}
.product .icons .icon_product:hover{
    background: var(--main_color);
}
.product .icons .icon_product i{
    font-size: 14px;
}
.product .icons .icon_product:hover i{
    color: var(--white_color);
}
.product .icons .btn_add_cart{
    background: var(--main_color);
    display: flex;
    align-items: center;
    gap: 5px;
    border-radius: 3px;
    color: var(--white_color);
    padding: 5px 10px;
    cursor: pointer;
    transition: 0.3s;
    border: 1px solid var(--main_color);
    font-size: 14px;
}
.product .icons .btn_add_cart i {
    color: var(--white_color);
}
.product .icons .btn_add_cart.active{
    color: var(--color_heading);
    background: transparent;
    pointer-events: none;
}
.product .icons .btn_add_cart.active i{
    color: var(--main_color);
}
.product .icons .btn_add_cart:hover{
    scale: 1.1;
    background:#f47600;

}

/* banners - product banner  */
.banners{
    margin: 50px 25px;
}
.banners .banners_boxs{
    display: flex;
    justify-content: space-between;
}
.banners .banners_boxs .box{
    width: 48%;
}
.banners .banners_boxs.banner_3_img .box{
    width: 32%;
}



/* start footer  */
footer{
    background: var(--color_heading);
}
footer .container{
    display: flex;
    justify-content: space-between;
    padding: 40px 10px;
}
footer .container  .big_row{
    width: 30%;
}
footer .container .big_row .logo_footer{
    width: 180px;
    font-size: 20px;
    font-weight: bolder;
    display: flex;
    line-height: 15px;
    justify-content: center;
    align-items: center;
}

footer .container .big_row .logo_footer i{


    color: var(--main_color);
    font-size: 40px;
}
footer .container .big_row .logo_footer span{
    color: white;
    font-size: 45px;
    padding-left: 15px;
}






footer .container .big_row p{
    width: 90%;
    color: #b1b1b1;
    font-size: 14px;
    line-height: 1.5;
    margin: 15px 0;
}
footer .container .big_row .icons_footer{
    display: flex;
    gap: 10px;
}
footer .container .big_row .icons_footer a{
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--main_color);
    border-radius: 5px;
    position: relative;
    top: 0;
    transition: 0.3s;
}
footer .container .big_row .icons_footer a:hover{
    top: -5px;
}
footer .container .big_row .icons_footer a i{
    color: var(--white_color);
    font-size: 20px;
}

footer .container .row{
    width: 22%;
}
footer .container .row h4{
    color: var(--white_color);
    font-size: 18px;
    margin-bottom: 20px;
}
footer .container .row .links{
    display: flex;
    flex-direction: column;
    gap: 7px;
}
footer .container .row .links a{
    color: #b1b1b1;
    font-size: 14px;
    position: relative;
    left: 0;
    transition: 0.3s;
}
footer .container .row .links a i {
    color: var(--main_color);
    font-size: 14px;
    margin-right: 5px;

}
footer .container .row .links a:hover{
    left: 8px;
    color: var(--main_color);
}
footer .bottom_footer{
    background: #1d1f22;
}
footer .bottom_footer .container{
    align-items: center;
    padding: 5px 0;
}
footer .bottom_footer p{
    color: var(--white_color);
}
footer .bottom_footer .payment_img{
    width: 350px;
}

/* start shopping cart  */

.cart{
    position: fixed;
    top: 0;
    right: -350px;
    bottom: 0;
    z-index: 1100;
    background: var(--white_color);
    border-left: 1px solid var(--border_color);
    width: 350px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: 0.5s ease-in-out;
}
.cart.active{
    right: 0;

}
.cart .top_cart{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cart .top_cart .close_cart i{
    cursor: pointer;
    font-size: 30px;
}
.cart .top_cart h3{
    font-size: 18px;
}
.cart .top_cart h3 span{
    color: var(--color_heading);
}
.cart .items_in_cart{
    padding: 10px 0;
    border-block: 1px solid var(--border_color);
    margin-block: 20px;
    height: 100%;
    overflow-y: auto;
}
.cart .items_in_cart .item_cart{
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    height: 125px;
    border-bottom: 1px solid var(--border_color);
}
.cart .items_in_cart .item_cart:last-child{
    border-bottom: 0;
    margin-bottom: 0;
}
.cart .items_in_cart .item_cart img{
    width: 80px;
}
.cart .items_in_cart .item_cart h4{
    margin-top: 0;
    margin-bottom: 3px;
    font-weight: 500;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.cart .items_in_cart .item_cart p{
    font-size: 14px;
    color: var(--main_color);
    margin-bottom: 5px;
}
.cart .items_in_cart .item_cart span {
    font-size: 14px;
    color: var(--main_color);
    display: block;
}

.cart .items_in_cart .item_cart .delete_item i{
    font-size: 22px;
    cursor: pointer;
    transition: 0.3s;
}
.cart .items_in_cart .item_cart .delete_item i:hover{
    color: #E51A1A;
}















.quantity_control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}


.quantity_control .quantity_input {
    width: 50px;
    height: 25px;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 5px;
    font-size: 16px;
}














.cart .items_in_cart .item_cart .quantity_control{
    display: flex;
    align-self: center;
    gap: 5px;
}
.cart .items_in_cart .item_cart .quantity_control span{
    font-size: 18px;
    min-width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
}
.cart .items_in_cart .item_cart .quantity_control button{
    width: 30px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 24px;
    border-radius: 5px;
    border: 1px solid var(--border_color);
}
.cart .bottom_cart .total{
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}
.cart .bottom_cart .price_cart_total{
    color: var(--main_color);
    font-size: 20px;
}
.cart .bottom_cart .button_cart{
    display: flex;
    gap: 20px;
}
.cart .bottom_cart .button_cart .btn_cart{
    text-transform: uppercase;
    font-size: 15px;
    border: 2px solid var(--main_color);
    cursor: pointer;
    border-radius: 5px;
    transition: 0.3s;
}
.cart .bottom_cart .button_cart .trans_bg{
    background: transparent;
    color: var(--color_heading);
}


.close_menu,.open_menu{
    display: none;
}


    /* checkout page  */

    .checkout{
        margin: 125px 0;
    }
    .checkout .container{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .checkout .ordersummary{
        width: 45%;
        padding: 0 20px;
        border-radius: 2px solid var(--border_color);
        border-radius: 5px;
        box-shadow: 0px 8px 10px #c0bfbf44;
    }
    .checkout .ordersummary h1{
        border-bottom: 1px solid var(--border_color);
        margin: 0;
        padding: 0 0 20px 0;
        color: var(--main_color);
    }
    .checkout .ordersummary .items{
        height: 350px;
        overflow-y: auto;
    }
    .checkout .ordersummary .item_cart{
        display: flex;
        gap: 20px;
        align-items: center;
        justify-content: space-between;
        height: 155px;
        border-bottom: 1px solid var(--border_color);
        padding-right: 20px;
    }
    .checkout .ordersummary .item_cart:last-child{
        border-bottom: 0;
    }
    .checkout .ordersummary .item_cart .image_name{
        display: flex;
        align-items: center;
        gap: 20px;
    }
    .checkout .ordersummary .item_cart img{
        width: 80px;
    }
    .checkout .ordersummary .item_cart .content{
        display: flex;
        flex-direction: column;
        margin: 5px 0;
    }
    .checkout .ordersummary .item_cart h4{
        margin-bottom: 10px;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .checkout .ordersummary .item_cart p{
        margin-bottom: 0;
    }
    .checkout .ordersummary .delete_item i{
        font-size: 22px;
        cursor: pointer;
        transition: 0.3s;
    }
    .checkout .ordersummary .delete_item i:hover{
        color: #E51A1A;
        scale: 1.1;
    }



    





































/* Make sure swiper slides have consistent width */
.slide_product .swiper-slide {
    height: auto;
    display: flex;
}

/* For duplicated slides in loop mode */
.swiper-slide-duplicate {
    display: flex !important;
}

/* Ensure product cards fill the slide height */
.slide_product .swiper-slide .product {
    width: 100%;
    height: 100%;
}

/* Ensure the swiper container has proper height */
.slide_product.mySwiper {
    height: auto;
    padding-bottom: 40px; /* Space for navigation arrows */
}

/* Fix for swiper navigation buttons */
.slide_product .swiper-button-next,
.slide_product .swiper-button-prev {
    top: 30px;
}
    .checkout .ordersummary .quantity_control{
        display: flex;
        align-items: center;
        gap: 5px;
        margin-top: 5px;
    }
    .checkout .ordersummary .quantity_control span{
        font-size: 18px;
        height: 27px;
        min-width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 2px;
        border: 1px solid var(--border_color);
        padding: 3px 0;
    }
    .checkout .ordersummary .quantity_control button{
        width: 27px;
        height: 27px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        border-radius: 2px;
        border: 1px solid var(--border_color);

    }
    .checkout .ordersummary .bottom_summary{
        border-top: 4px solid var(--border_color);
        padding-top: 25px;
    }
    .checkout .ordersummary .shope_table{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .checkout .ordersummary .shope_table p{
        font-size: 20px;
        color: var(--color_heading);
        text-transform: capitalize;
        margin-bottom: 25px;
    }
    .checkout .ordersummary .shope_table span{
        font-weight: bold;
        font-size: 20px;
    }
    .checkout .ordersummary .button_div{
        border-top: 1px solid var(--border_color);
        padding: 15px 0;
    }
    .checkout .ordersummary .button_div button{
        width: 100%;
        background: var(--main_color);
        color: var(--white_color);
        border: 2px solid var(--main_color);
        padding: 15px 0;
        outline: none;
        border-radius: 5px;
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
    }
    .checkout .ordersummary .button_div button:hover{
        background: transparent;
        color: var(--main_color);
    }
    .checkout .input_info{
        width: 45%;
    }
    .checkout .input_info h2{
        margin-top: 0;
        padding: 10px 20px;
        background: var(--main_color);
        text-transform: capitalize;
        color: var(--white_color);
    }
    .checkout .input_info .address,
    .checkout .input_info .coupon{
        margin-bottom: 25px;
        border-radius: 5px;
        overflow: hidden;
        border: 1px solid var(--border_color);
        box-shadow: 0px 8px 10px #c0bfbf44;
    }
    .checkout .input_info .coupon .btn_coupon {
        text-align: center;
        padding-bottom: 20px;
    }

    .checkout .input_info .coupon .btn_coupon button{
        width: 50%;
        background: var(--main_color);
        color: var(--white_color);
        border: 2px solid var(--main_color);
        padding: 12px 0;
        outline: none;
        border-radius: 5px;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        transition: 0.3s ease-in-out;

    }
    .checkout .input_info .coupon .btn_coupon button:hover{
        background: transparent;
        color: var(--main_color);
    }
    .checkout .input_info .inputs{
        display: flex;
        flex-direction: column;
        padding: 15px 20px;
    }
    .checkout .input_info .inputs label{
        color: var(--color_heading);
        font-size: 14px;
        text-transform: capitalize;
    }
    .checkout .input_info .inputs input{
        margin: 8px 0 22px;
        padding: 15px 10px;
        outline: none;
        border: 1px solid var(--border_color);
        border-radius: 5px;
        transition: 0.3s;
    }
    .checkout .input_info .inputs input:focus{
        border-color: var(--main_color);
    }



/* start responsive  */


/* Checkout form success and error messages */
.success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
    font-weight: bold;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
    font-weight: bold;
}

/* Disabled button style */
button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Form validation styles */
input:invalid {
    border-color: #dc3545;
}

input:focus:invalid {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Required field indicator */
.input_info label::after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
}

.input_info label[for="coupon"]::after {
    content: "";
    margin-top: 0; /* Remove margin that creates gap */
    top: 100%; /* Position directly below parent */
    background-color: #f9f9f9;
    min-width: 200px;
    border-radius: 5px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    padding: 5px;

}

.no-subcats {
    padding: 10px 15px;
    color: #999;
    font-style: italic;
}

@media (max-width:1100px) {

header .top_header .container{
    flex-wrap: wrap;
}
header .top_header .search_box{
    order: 3;
    width: 80%;
    margin: 20px auto 0;
}
header .top_header .logo{
    width: 230px;
}
header .top_header .search_box select{
    width: 200px;
}
header .top_header .search_box input{
    width: calc(100% - 200px);
}
header .bottom_header .nav_links{
    position: fixed;
    top: 0;
    left: -400px;
    bottom: 0;
    background: #fff;
    width: 400px;
    flex-direction: column;
    align-items: center;
    padding-top: 100px;
    border: 1px solid #b5b5b596;
    transition: 0.3s ease-in-out;
}
header .bottom_header .nav_links.active{
    left: 0px;
    z-index: 1;
}
header .bottom_header .nav_links li{
    font-size: 18px;
    height: auto;
}
.close_menu{
    display: block;
    position: absolute;
    top: 30px;
    right: 30px;
    font-size: 30px;
}
.open_menu{
    display: block;
    font-size: 25px;
}
.open_menu i{
    color: var(--main_color);
    border: 2px solid var(--main_color);
    height: 40px;
    width: 40px;
    text-align: center;
    line-height: 40px;
    border-radius: 5px;
}
header .bottom_header .category_nav{
    width: auto;
}
header .bottom_header .category_nav .category_btn p{
    font-size: 13px;
    margin: 0 10px;
}
header .bottom_header nav{
    gap: 20px;
}
.banners_4 .container{
    flex-wrap: wrap;
}
.banners_4 .container .box{
    width: 49%;
    margin-bottom: 20px;
    justify-content: space-around;

}

}

@media (max-width:1000px){
.slider .banner_2{
    display: none;
}
.slider .container .slide-swp{
    width: 100%;
}

footer .container{
    flex-wrap: wrap;
}
footer .container .big_row {
    width: 40%;
    margin-bottom: 20px;
}
footer .container .row {
    width: 30%;
    margin-bottom: 20px;

}
.checkout .container{
    flex-direction: column;
    gap: 50px;
}
.checkout .ordersummary,
.checkout .input_info{
    width: 80%;
}



}




@media (max-width:999px){

header .bottom_header {
    position: fixed;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-self: start;
    left: 0;
    top: 70px;
    width: 320px;
    height: 100%;
    background-color: hsl(229, 54%, 51%);
    /* background-color:white; */
    padding: 15px;
    overflow-y: auto;
    z-index: 1;
    transform: translateX(-50%);  
    /* ????????? */
}

header .bottom_header.open {
    transform: none;
}

header .bottom_header .cat-menu {
    display: flex;
    height: 100%;
    align-items: flex-start;
    justify-content: left;
    margin: 15px 0;
}


header .bottom_header .cat-menu ul{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    width: 100%;
    background-color: hsl(229, 54%, 51%);
}
header .bottom_header .cat-menu ul li {
    margin: 5px 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 45px 0 0;
}
header .bottom_header .cat-menu ul li.dropdown a {
    padding-right: 5px;
}

header .bottom_header .cat-menu > ul > li > a {
    padding: 12px 0;
}


header .bottom_header .cat-menu ul li.dropdown ul.sub-menu li.dropdown ul.sub-menu.sub-menu-right {
    margin-top: -28px;
}






header .bottom_header .close-menu-btn {
    height: 35px;
    width: 35px;
    position: absolute;
    top: 25px;
    right: 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    cursor: pointer;
    border: none;
}
header .bottom_header .close-menu-btn::before,
header .bottom_header .close-menu-btn::after{
    content: '';
    position: absolute;
    width: 80%;
    height: 2px;
    background-color: #000;
}
header .bottom_header .close-menu-btn::before {
    transform: rotate(45deg);
}
header .bottom_header .close-menu-btn::after {
    transform: rotate(-45deg);

}
header .bottom_header .cat-menu ul li {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    padding: 0 10px;
}
header .bottom_header .cat-menu > ul > li:not(:last-child) {
    margin-right: 0;
}
header .bottom_header li a {
    border-bottom: 1px solid hsla(0, 0%, 100%, 0.25);
}

header .bottom_header .cat-menu ul li.dropdown i.fa-solid.fa-chevron-down {
    height: 34px;
    width: 34px;
    border: 1px solid hsla(0, 0%, 100%, 0.25);
    align-items: center;
    display: inline-flex;
    justify-content: center;
    pointer-events: auto;
    cursor: pointer;
    position: absolute;
    top: 7px;
    right: 25px;
}
header .bottom_header .cat-menu ul li.dropdown ul.sub-menu li.dropdown i.fa-solid.fa-chevron-down {
position: relative;
left: 84%;
top: -40px;
z-index: 1;
}
header .bottom_header .sub-menu {
    position: static;
    opacity: 1;
    transform: none;
    visibility: visible;
    padding: 0;
    /* background-color: hsl(229, 54%, 51%); */
    transition: none;
    box-shadow: none;
    width: 100%;

}
 /* hide first dropdown menu  */
 /* hide first dropdown menu  */
 /* hide first dropdown menu  */
 /* hide first dropdown menu  */
header .bottom_header .cat-menu .dropdown .sub-menu {
    display: none;
}

header .bottom_header .sub-menu li:last-child {
    border: none;
}

header .bottom_header .sub-menu a {
    padding: 12px 0 12px 15px;
}
header .bottom_header .sub-menu .sub-menu a {
    padding-left: 30px;
}
header .bottom_header .sub-menu .sub-menu .sub-menu a {
    padding-left: 45px;
}

header .bottom_header .menu-right .open-menu-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    width: 44px;
    cursor: pointer;
    position: relative;
    background-color: transparent;
    border: none;

}
header .bottom_header .menu-right .open-menu-btn .line {
    height: 2px;
    width: 30px;
    background-color: #000;
    position: absolute;

}
header .bottom_header .menu-right .open-menu-btn .line-1 {
    transform: translateY(-8px);
}
header .bottom_header .menu-right .open-menu-btn .line-3 {
    transform: translateY(8px);
}










}






@media (max-width:800px){

    .top_slide h2{
        font-size: 16px;
        padding: 10px 25px;
    }
    .product .name_product{
        font-size: 14px;
    }
    .price p{
        font-size: 16px;
    }
    .price .old_price{
        font-size: 12px;
    }
    .product .icons .btn_add_cart{
        font-size: 14px;
        padding: 3px 5px;
    }
    .banners .banners_boxs{
        flex-wrap: wrap;
    }
    .banners .banners_boxs .box{
        width: 100%;
        margin-bottom: 20px;
    }
    .banners .banners_boxs.banner_3_img .box{
        width: 49%;
    }


    footer .container .big_row {
        width: 50%;
        margin-bottom: 20px;
    }
    footer .container .row {
        width: 50%;
        margin-bottom: 20px;
    }
    footer .bottom_footer .container{
        flex-direction: column;
        gap: 10px;
    }








}

@media (max-width:600px) {
header .bottom_header .category_nav{
    display: none;
}
header .top_header .search_box{
    width: 100%;
}
header .top_header .search_box select {
    width: 140px;
}
header .top_header .search_box input{
    width: calc(100% - 140px);
}
header .bottom_header .nav_links{
    width: 100%;
    left: -100%;
}
.cart{
    width: 100%;
    right: -100%;
}

.banners_4 .container .box{
    width: 100%;
}
.product .icons .btn_add_cart.active{
    font-size: 12px;
}
.banners_4 .container .box h5{
    font-size: 12px;
}
.banners_4 .container .box h6{
    font-size: 11px;
}
.banners .banners_boxs.banner_3_img .box{
    width: 100%;
}

.product .icons .btn_add_cart{
    font-size: 12px;
}
.product .icons .btn_add_cart.active i{
    font-size: 12px;
}
footer .container .row{
    width: 100%;
}
footer .container .big_row{
    width: 100%;
}

.checkout .ordersummary,
.checkout .input_info{
    width: 100%;
}
.checkout .input_info h2 {
    font-size: 20px;
}

}

    
    /* Responsive checkout page  */



        
@media screen and (min-width: 481px) {
    /* .products.main .item{
        flex: 0 0 50%;
    
    } */

    
    
}




    
@media screen and (min-width: 768px) {
.products.one .row{
    /* padding-top: 90px; */
    flex: 0 0 50%;
    width: 50%;
    /* margin-top: 90px; */
    
}
.products.one .is_sticky,
.is_sticky {
    position: sticky;
    /* top: 20%; */
}





}


@media screen and (min-width: 992px) { 

   /* added by me*/
   .products.one .row {  
    flex: 0 0 50%; 
    width: 50%;  
    margin-left: 5px;   
}   
/* added by me*/


.container{
    width: 100%;
    padding: 0 15px;
}

.desktop-hide {
    display: none;

}
.mobile-hide {
    display: block;

}
.logo a {
    margin-right: 2em;
    height: 30px;
}
    

/* Page single */
.products.one .flexwrap > .row:last-child > .item {
    padding-left: 0;
}

.single-category .holder {
    flex-direction: row;
    flex-wrap: wrap;
}
.single-category .sidebar {
    flex: 1 0 15%;
}
.single-category .section {
    flex: 1 0 78%;

}
.single-category .sidebar .filter {
    padding: 40px 10px 20px 25px;
    position: sticky;
    top: 2em;
    box-shadow: none;
    width: var(--percent100);
    z-index: initial;
    visibility: visible;
    opacity: 1;
}
.single-category .products.main .product {
    
    /* flex: 0 0 33.3333%; */

}





}
