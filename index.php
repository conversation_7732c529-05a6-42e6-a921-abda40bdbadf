<?php

ob_start();

session_start();

$pageTitle = 'Homepage';

include 'init.php'; 



?>


    <body>

        <div id="page" class="site page-home">
  
            <div class="productsDetils">

                <?php

                    $initData = [];

                    $productsFile = 'products.json';


                    $productsResults = getAllfrom("*", "items", "WHERE Approve = 1", "", "", "Name");

    
                    // Loop through the result set and populate the initData array

                    foreach ($productsResults as $row) {
                        $initData[] = [
                            'Item_ID'       => $row['Item_ID'], // Access as an associative array
                            'Name'          => $row['Name'],
                            'Description'   => $row['Description'],
                            'Price'         => $row['Price'],
                            'Rating'        => $row['Rating'],
                            'Cat_ID'        => $row['Cat_ID'],
                            'tags'          => $row['tags'],
                            'Brand'         => $row['Brand'],
                            'discount'      => $row['discount'],
                            'priceBefore'   => $row['priceBefore'],
                            'sectionID'     => $row['sectionID'],
                            'views'         => $row['views'],
                            'newarraived'   => $row['newarraived'],
                            'subcat'        => $row['subcat'],
                            'pic1'          => $row['pic1'],
                            'pic2'          => $row['pic2'],
                            'pic3'          => $row['pic3'],
                            'pic4'          => $row['pic4'],
                            'pic5'          => $row['pic5'],
                            'hotDeal'       => $row['hotDeal'],
                            'trend'         => $row['trend']
                        ];
                    }

                    if(file_exists($productsFile)){

                    file_put_contents($productsFile, json_encode($initData));
    
                    } else{ echo 'Not';}

                    $viewtring = json_encode($initData);
                    ?>

               <p> <?php echo $viewtring ; ?></p>

            </div>
                
            <div class="cart">
                <div class="top_cart">
                    <h3>Cart items : <span class="count_item_cart">0</span></h3>
                    <span onclick="open_close_cart()" class="close_cart"><i class="fa-regular fa-circle-xmark"></i></span>
                </div>

                <div class="items_in_cart" id="cart_items">
                    
                </div>


                <div class="bottom_cart">
                    <div class="total">
                        <p>subtotal</p>
                        <p class="price_cart_total">$0</p>
                    </div>

                    <div class="button_cart">
                        <a href="checkout.php" class="btn_cart btn">Checkout</a>
                        <span onclick="open_close_cart()" class="btn_cart trans_bg btn">Shope More</span>
                    </div>
                </div>


            </div>

            <header>
                <div class="top_header">
                    <div class="container">
                        <a href="index.php" class="logo"><i class="fa-solid fa-laptop"></i>
                        
                        <span>Tikmart</span> 
                        
                        </a>
                
                        
                        <form action="" class="search_box">

                            <div class="select_box">
                                <select id="category" name="category">

                                    <option value="All Categories">All Categories</option>
                                    <option value="Electronics & Digital">Electronics & Digital</option>
                                    <option value="Phone & Tablet">Phone & Tablet</option>
                                    <option value="Fashion & Clothins">Fashion & Clothins</option>
                                    <option value="Televsion & Monitor">Televsion & Monitor</option>
                                    <option value="Jewelry & Watches">Jewelry & Watches</option>
                                    <option value="Washing Machine">Washing Machine</option>
                                    <option value="Toys & Hobbies">Toys & Hobbies</option>


                                </select>
                            </div>

                            <input type="text" name="search" id="search" placeholder="Search for products" required>

                            <button type="submit">
                                <i class="fa-solid fa-magnifying-glass"></i>
                            </button>

                        </form>

                        <div class="header_icons">
                            <!-- <div class="icon">
                                <a href="#">
                                    <i class="fa-solid fa-heart"></i>
                                    <span class="count count_favourite">0</span>
                                </a>
                            </div> -->


                            
                            <div onclick="open_close_cart()" class="icon">
                                    <i class="fa-solid fa-cart-shopping"></i>
                                    <span class="count count_item_header">0</span>
                                
                            </div>




                        </div>
                    </div>
                </div>









             
                <div class="bottom_header">

                    <nav class="cat-menu">

                        <div class="menu-top">
                            <button type="button" class="close-menu-btn"></button>
                        </div>

                        <ul>
                            <li><a href="#"><span>Home</span></a></li>

                            <li class="dropdown">
                                <a href="#"><span>Categories</span></a>
                                <i class="fa-solid fa-chevron-down"></i>

                                <ul class="sub-menu">
                                    <li><a href=""><span>Bits</span></a></li>
                                    
                                    <li class="dropdown">
                                        <a href=""><span>Tools</span></a>
                                        <i class="fa-solid fa-chevron-down"></i>
                                        <ul class="sub-menu sub-menu-right">
                                            <li><a href="#"><span>Tool 1</span></a></li>
                                            <li><a href="#"><span>Tool 2</span></a></li>
                                            <li><a href="#"><span>Tool 3</span></a></li>
                                            <li><a href="#"><span>Tool 4</span></a></li>



                                            <li class="dropdown">
                                                <a href="#"><span>Tool 5</span></a>
                                                <i class="fa-solid fa-chevron-down"></i>
                                                <ul class="sub-menu sub-menu-right">
                                                    <li><a href=""><span>CAT 31</span></a></li>
                                                    <li><a href=""><span>CAT 32</span></a></li>
                                                    <li><a href=""><span>CAT 33</span></a></li>
                                                    <li><a href=""><span>CAT 34</span></a></li>
                                                    <li><a href=""><span>CAT 35</span></a></li>
                                                </ul>

                                            </li>



                                            <li><a href="#"><span>Tool 6</span></a></li>
                                            <li><a href="#"><span>Tool 7</span></a></li>

                                        </ul>

                                    </li>
                                    
                                    <li><a href=""><span>Meals</span></a></li>
                                    <li><a href=""><span>Travel</span></a></li>
                                    <li><a href=""><span>Sports</span></a></li>
                                </ul>
                            </li>


                            <li><a href="#"><span>Contact</span></a></li>

                            <li class="dropdown">
                                <a href="#"><span>Items</span></a>
                                <i class="fa-solid fa-chevron-down"></i>
                                <ul class="sub-menu">
                                    <li><a href=""><span>Items 1</span></a></li>
                                    <li><a href=""><span>Items 2</span></a></li>
                                    <li class="dropdown">
                                        <a href=""><span>Items 3</span></a>
                                        <i class="fa-solid fa-chevron-down"></i>
                                        <ul class="sub-menu sub-menu-right">
                                            <li><a href="#"><span>3XXXX 1</span></a>
                                            <li><a href="#"><span>3XXXX 1</span></a>
                                            <li><a href="#"><span>3XXXX 1</span></a>
                                            <li><a href="#"><span>3XXXX 1</span></a>
                                            
                                    
                                        </ul>
                                    </li>
                                    <li><a href=""><span>Items 4</span></a></li>
                                    <li><a href=""><span>Items 5</span></a></li>
                                </ul>
                            </li>


                        </ul>

                    </nav>


                    <div class="menu-right">
                        <button type="button" class="search-btn icon-btn"><i class="fa-solid fa-search"></i></button>
                        <button type="button" class="cart-btn icon-btn"><i class="fa-solid fa-shopping-cart"></i></button>
                        <button type="button" class="open-menu-btn">
                            <span class="line line-1"></span>
                            <span class="line line-2"></span>
                            <span class="line line-3"></span>
                        </button>
                    </div>


   
                    










                    <div class="container">


                        <nav class="nav">
                  

                        
                            <ul class="menu-items">
                                <li><a href="#" class="menu-item">Home</a></li>

                                <!-- multilevel dropdown menu  -->
                                <li class="dropdown">
                                    <a href="#" class="menu-item">Dropdown Menu</a>
                                    <ul class="dropdown-menu">
                                        <li><a href="#" class="menu-item">Item 1</a></li>
                                        <li><a href="#" class="menu-item">Item 2</a></li>
                                        <li><a href="#" class="menu-item">Item 3</a></li>
                                        <li><a href="#" class="menu-item">Item 4</a></li>
                                    </ul>
                                </li>












                                <li><a href="#" class="menu-item">Mega Menu</a></li>
                                <li><a href="#" class="menu-item">Blog</a></li>
                                <li><a href="#" class="menu-item">About</a></li>
                            </ul>





                            <!-- 


                            <span onclick="open_Menu()" class="open_menu"><i class="fa-solid fa-bars"></i></span>

                            


                            <ul class="nav_links">

                                <span onclick="open_Menu()" class="close_menu"><i class="fa-regular fa-circle-xmark"></i></span>
                                
                                <li class="active"><a href="index.php">Home</a></li>

                                



                                    <?php
                                    $allSections = getAllfrom('*', 'cat', 'where Visibility = 0', 'and section = 1', '' ,'Name');
                                    foreach ($allSections as $section){
                                        echo '<div class="dropdown">'; 
                                        echo '<button class="dropbtn"><a href="cat.php?do='. $section['ID'] .'&subid=1"">'. $section['Name'].'</a> &#10552;</button>';
                                        
                                        echo '<div class="dropdown-content">';

                                        // Fetch only parent categories that belong to this section
                                        $allCategories = getAllfrom('*', 'cat', 'where Visibility = 0 AND parent = 0', 'and section = '.$section['ID'], '' ,'Name');
                                        foreach ($allCategories as $category){
                                            // Create a container for the parent category with right-dropdown
                                            echo '<div class="dropdown-right">';
                                            


                                            // Parent category link with right arrow icon
                                            // echo '<a href="cat.php?do='. $category['ID'] .'&subid=' . $category['parent'] . '" class="parent-cat">'. $category['Name'] .' <i class="fa fa-angle-right"></i></a>';
                                            echo '<a href="cat.php?do='. $category['ID'] .'&subid=2" class="parent-cat">'. $category['Name'] .' <i class="fa fa-angle-right"></i></a>';
                                            


                                            
                                            // Dropdown content for subcategories
                                            echo '<div class="dropdown-content-right">';
                                            
                                            // Fetch subcategories for this parent
                                            $subCategories = getAllfrom('*', 'cat', 'where Visibility = 0 AND parent = '.$category['ID'], '', '' ,'Name');
                                            if (!empty($subCategories)) {
                                                foreach ($subCategories as $subCategory){
                                                    // Make sure both parameters are properly formatted
                                                    // echo '<a href="cat.php?do='. $subCategory['ID'] .'&subid=' . $subCategory['parent'] . '">'. $subCategory['Name'] .'</a>';
                                                    echo '<a href="cat.php?do='. $subCategory['ID'] .'&subid=3">'. $subCategory['Name'] .'</a>';
                                                    
                                                    // For debugging, you can print the URL to verify it's correct
                                                    // echo "<!-- Debug URL: cat.php?do=". $subCategory['ID'] ."&subid=" . $category['ID'] . " -->";
                                                }
                                            } else {
                                                echo '<span class="no-subcats">No subcategories</span>';
                                            }
                                            
                                            echo '</div>'; // End dropdown-content-right
                                            echo '</div>'; // End dropdown-right
                                        }
                                        
                                        echo '</div>'; // End dropdown-content
                                        echo '</div>'; // End dropdown
                                    }
                                ?>
                                -->
                            </ul>


                        </nav>




                        <div class="login_signup btns">


                            <!-- if thier is a user signed in show his name else will show login and signup buttons -->
                            <?php

                            if (isset($_SESSION['user'])){
                            

                                // note variable $sessionUser = $_SESSION['user']; 

                                echo 'Hi ' . $sessionUser . ' ';

                                echo '<a href="profile.php">My Profile </a>';
                            
                                echo '<a href="logout.php">- Logout</a>';
                                
                                $userStatus = checkUserStatus($sessionUser);
                            
                                if ($userStatus == 1) {
                                    // user in not active 

                                    echo 'Your member ship not approved yet';
                                }

                            } else {
                                

                            ?>
                            <a href="login.php" class="btn"><span>Login</span> <i class="fa-solid fa-right-to-bracket"></i></a>
                            <a href="signup.php" class="btn"><span>Sign Up</span><i class="fa-solid fa-user-plus"></i></a>
                        
                            <?php } ?>
                        
                        </div>


                    </div>




                </div>





            </header>

            <!-- slider banner  -->

            <div class="slider">
                <div class="container">

                    <!-- Swiper -->

                    
                    
                    <!-- banner left -->
                    
                    <!-- <div class="banner_2">
                        <a href="#"><img src="img/banner_home3.png" alt=""></a>
                    </div>  -->


                    <div class="slide-swp mySwiper">

                        <div class="swiper-wrapper">

                            <div class="swiper-slide">
                                <a href="#"><img src="img/banner_home1.png" alt=""></a>

                            </div>

                            <div class="swiper-slide">
                                <a href="#"><img src="img/banner_home2.png" alt=""></a>

                            </div>


                            <div class="swiper-slide">
                                <a href="#"><img src="img/banner_home1.png" alt=""></a>

                            </div>

                        </div>

                        <!-- <div class="swiper-button-next-1"></div>
                        <div class="swiper-button-prev-1"></div> -->
                        <div class="swiper-pagination"></div>

                    </div>

                    <div class="banner_2">
                        <a href="#"><img src="img/banner_home3.png" alt=""></a>
                    </div>

                </div>

            </div>


            <!-- banners  -->

            <div class="banners_4">
                <div class="container">


                    <div class="box">
                        <a href="#" class="link_btn"></a>

                        <img src="img/banner3_1.png" alt="">

                        <div class="text">
                            <h5>Break Disc</h5>
                            <h5>deals on this</h5>
                            <div class="sale">
                                <p>Up <br> to</p>
                                <span>70%</span>
                            </div>
                            <h6>Shop Now</h6>

                        </div>
                    </div>



                    <div class="box">
                        <a href="#" class="link_btn"></a>

                        <img src="img/banner3_2.png" alt="">

                        <div class="text">
                            <h5>Break Disc</h5>
                            <h5>deals on this</h5>
                            <div class="sale">
                                <p>Up <br> to</p>
                                <span>70%</span>
                            </div>
                            <h6>Shop Now</h6>
                        </div>
                    </div>



                    <div class="box">
                        <a href="#" class="link_btn"></a>

                        <img src="img/banner3_3.png" alt="">

                        <div class="text">
                            <h5>Break Disc</h5>
                            <h5>deals on this</h5>
                            <div class="sale">
                                <p>Up <br> to</p>
                                <span>70%</span>
                            </div>
                            <h6>Shop Now</h6>
                        </div>
                    </div>


                    <div class="box">
                        <a href="#" class="link_btn"></a>

                        <img src="img/banner3_4.png" alt="">

                        <div class="text">
                            <h5>Break Disc</h5>
                            <h5>deals on this</h5>
                            <div class="sale">
                                <p>Up <br> to</p>
                                <span>70%</span>
                            </div>
                            <h6>Shop Now</h6>
                        </div>
                    </div>





                </div>
            </div>

            <!-- Hot deal products slider  -->
            <div class="slider_products slide">
                <div class="container">
                    <div class="slide_product mySwiper">
                        <div class="top_slide">
                            <h2><i class="fa-solid fa-tags"></i>Hot Deals</h2>
                        </div>
                        <div class="products swiper-wrapper" id="swiper_hot_deals">
                            <?php
                                
                                
                                // Use a single WHERE clause with both conditions
                                $allHotDealsItems = getAllfrom('*', 'items', 'Where Approve = 1 AND hotDeal = 1', '', '', 'Name');
                                
                                
                                

                                if (empty($allHotDealsItems)) {
                                    echo '<div class="swiper-slide"><div class="product"><p>No hot deals available at the moment.</p></div></div>';
                                } else {
                                    foreach ($allHotDealsItems as $hotDealItem) {

                                    // Calculate discount percentage if old price exists

                                    // 1- price
                                    if (isset($hotDealItem["Price"]) && is_numeric($hotDealItem["Price"])) {
                                        $priceNew = floatval($hotDealItem["Price"]); // Use floatval to ensure it's a numeric value
                                        $priceNewVlue = number_format(floatval($hotDealItem["Price"]), 2);
                                    } else {
                                        $priceNew = 0.00; // Default value if Price is missing or invalid
                                    }

                                    // 2- priceBefore
                                    if (isset($hotDealItem["priceBefore"]) && is_numeric($hotDealItem["priceBefore"])) {
                                        $priceOld = floatval($hotDealItem["priceBefore"]); // Use floatval to ensure it's a numeric value
                                        $priceOldVlue = number_format(floatval($hotDealItem["priceBefore"]), 2);

                                    } else {
                                        $priceOld = 0.00; // Default value if priceBefore is missing or invalid
                                    }


                                        echo '<div class="swiper-slide">';
                                        echo '<div class="product">';
                                        
                                        // 3- discount
                                        if ($priceOld > 0) {
                                            $discountCalc = (($priceOld - $priceNew) / $priceOld) * 100;
                                            $discountPercent = number_format($discountCalc, 0); 
                                        echo '<span class="sale_percent">' . $discountPercent . '%</span>';

                                        } else {
                                            $discountPercent = 0;
                                        }

                                        echo '<div class="img_product">';
                                        echo '<a href="page-single.php?do=' . $hotDealItem["Item_ID"] . '"><img src="admin/uploads/items/' . $hotDealItem["pic1"] . '" alt=""></a>';
                                        echo '</div>';
                                        echo '<div class="stars">';
                                        echo '<i class="fa-solid fa-star"></i>';
                                        echo '<i class="fa-solid fa-star"></i>';
                                        echo '<i class="fa-solid fa-star"></i>';
                                        echo '<i class="fa-solid fa-star"></i>';
                                        echo '<i class="fa-solid fa-star"></i>';
                                        echo '</div>';
                                        echo '<p class="name_product"><a href="page-single.php?do=' . $hotDealItem["Item_ID"] . '"> ' . $hotDealItem["Name"] . '</a></p>';
                                        
                                        echo '<div class="price">';
                                        echo '<p><span>EGP ' . $priceNewVlue . '</span></p>';
                                        // Show old price only if it's greater than 0
                                        if ($priceOld > 0) {
                                            echo '<p class="old_price">EGP ' . $priceOldVlue . '</p>';
                                        }

                                        echo '</div>';
                                        echo '<div class="icons">';

                                        echo '<span class="btn_add_cart" data-id="' . $hotDealItem["Item_ID"] . '">';
                                        echo '<i class="fa-solid fa-cart-shopping"></i>Add to cart';    
                                        
                                        echo '</span>';
                                        echo '<span class="icon_product">';
                                        echo '<i class="fa-regular fa-heart"></i>';    
                                        echo '</span>';
                                        echo '</div>';
                                        echo '</div>';
                                        echo '</div>';
                                    }
                                }
                            ?>
                        </div>
                        <div class="swiper-button-next btn_Swip"></div>
                        <div class="swiper-button-prev btn_Swip"></div>
                    </div>
                </div>
            </div>

            <!-- products banner  -->
            <div class="banners">
                <div class="container">
                    <div class="banners_boxs banner_2_img">

                        <a href="#" class="box"><img src="img/banner_box4.jpg" alt=""></a>
                        <a href="#" class="box"><img src="img/banner_box5.jpg" alt=""></a>
                    </div>
                </div>
            </div>


            <!-- Electronics products slider -->
            <div class="slider_products slide">
                <div class="container">
                    <div class="slide_product mySwiper">
                        <div class="top_slide">
                            <h2><i class="fa-solid fa-tags"></i> Electronics </h2>
                        </div>
                        
                        <div class="products swiper-wrapper" id="swiper_electronics">
                            <!-- Products will be loaded here via JavaScript -->
                            <!-- Fallback content if no products are loaded -->
                            <div class="swiper-slide">
                                <div class="product">
                                    <div class="img_product">
                                        <img src="img/product_1.jpg" alt="Loading...">
                                    </div>
                                    <div class="product_info">
                                        <h3>Loading products...</h3>
                                        <div class="description">Please wait while we load the products.</div>
                                        <div class="price">
                                            <span class="current">$0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="swiper-button-next btn_Swip"></div>
                        <div class="swiper-button-prev btn_Swip"></div>
                    </div>
                </div>
            </div>


            <!-- Appliances products slider  -->

            <div class="slider_products slide">
                <div class="container">

                    <div class="slide_product mySwiper">

                        <div class="top_slide">
                            <h2><i class="fa-solid fa-tags"></i> Appliances </h2>
                        </div>


                        <div class="products swiper-wrapper" id="swiper_appliances">    

                            

                        </div>

                            <div class="swiper-button-next btn_Swip"></div>
                            <div class="swiper-button-prev btn_Swip"></div>


                    </div>

                </div>
            </div>




            <!-- 3 Banners  -->

            <div class="banners">
                <div class="container">
                    <div class="banners_boxs banner_3_img">
                        <a href="#" class="box"><img src="img/banner_box1.jpg" alt=""></a>
                        <a href="#" class="box"><img src="img/banner_box2.jpg" alt=""></a>
                        <a href="#" class="box"><img src="img/banner_box3.jpg" alt=""></a>
                    </div>
                </div>
            </div>




            <!-- Mobiles products slider  -->

            <div class="slider_products slide">
                <div class="container">

                    <div class="slide_product mySwiper">

                        <div class="top_slide">
                            <h2><i class="fa-solid fa-tags"></i> Mobiles </h2>
                        </div>


                        <div class="products swiper-wrapper" id="swiper_mobiles">    

                            

                        </div>

                            <div class="swiper-button-next btn_Swip"></div>
                            <div class="swiper-button-prev btn_Swip"></div>


                    </div>

                </div>
            </div>


        </div>



        
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

        <!-- file JS -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/fslightbox/3.0.9/index.js"></script>
        <script src="js/swiper.js"></script>
        <script src="js/items_home.js"></script>
        <script src="js/main.js"></script>

        
<!-- 
    <script>
        const FtoShow = '.filter';
        const Ftopup = document.querySelector(FtoShow);
        const Ftrigger = document.querySelector('.filter-trigger');

        Ftrigger.addEventListener('click', () => {
            setTimeout(() => {
                if(!Ftopup.classList.contains('show')) {
                    Ftopup.classList.add('show')
                }
            }, 250 )
        })

        // auto close by click outside .filter 
        document.addEventListener('click', (e) => {
            const isClosest = e.target.closest(FtoShow);
            if(!isClosest && Ftopup.classList.contains('show')) {
                Ftopup.classList.remove('show')
            }
        })
        
        // show modal on load 

        window.onload = function() {
            document.querySelector('.cart').classList.toggle('showmodal')
        }
        document.querySelector('.modalclose').addEventListener('click', function(){
            document.querySelector('.cart').classList.remove('showmodal')
        })
    </script> -->



<?php
include $tpl . 'footer.php';
ob_end_flush();
?>


